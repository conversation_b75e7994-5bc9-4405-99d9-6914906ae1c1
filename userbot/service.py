import asyncio
from typing import Optional

from userbot.external.kurigram.managers import session


class UserbotService:
    """
    Service for userbot.
    """
    @classmethod
    def send_verification_code(
        cls,
        session_name: str,
    ) -> Optional[str]:
        """
        Send verification code to the user.
        """
        return asyncio.run(
            session.send_verification_code(
                session_name,
            )
        )

    @classmethod
    def verify_code(
        cls,
        session_name: str,
        code: str,
        phone_code_hash: str,
        two_fa_password: str = None,
    ) -> bool:
        """
        Verify code.
        """
        return asyncio.run(
            session.verify_code(
                session_name,
                code,
                phone_code_hash,
                two_fa_password,
            )
        )

    @classmethod
    def logout(cls, session_name: str) -> bool:
        """
        Logout from the session.
        """
        return asyncio.run(session.logout(session_name))


userbot = UserbotService()
