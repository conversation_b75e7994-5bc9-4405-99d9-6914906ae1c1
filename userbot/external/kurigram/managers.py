import os
import json
from contextlib import asynccontextmanager
from typing import Optional, Dict, Any, AsyncGenerator

from pyrogram import Client
from pyrogram.errors import SessionPasswordNeeded
from asgiref.sync import sync_to_async

from backend.settings import STATIC_ROOT


class KurigramSessionManager:
    """
    Manager for Kurigram sessions.
    """
    def __init__(
        self,
        sessions_file: str = os.path.join(STATIC_ROOT, "sessions.json"),
    ):
        self.sessions_file = sessions_file

    def _get_session_data(self, session_name: str) -> Dict[str, Any]:
        """
        Get session credentials from the sessions file.
        """
        try:
            with open(self.sessions_file, "r", encoding="utf-8") as f:
                data = json.load(f)
                session_data = next(
                    (item for item in data if item["session_name"] == session_name), # noqa
                    None
                )
                if not session_data:
                    raise ValueError(
                        f"Session '{session_name}' not found"
                    )
                return session_data
        except FileNotFoundError as exc:
            raise ValueError(
                f"Sessions file not found: {self.sessions_file}"
            ) from exc
        except json.JSONDecodeError as exc:
            raise ValueError(
                "Invalid sessions file format"
            ) from exc

    @asynccontextmanager
    async def _get_client(
        self, session_name: str
    ) -> AsyncGenerator[Client, None]:
        """
        Context manager for handling Kurigram client connections.
        """
        session_data = self._get_session_data(session_name)
        client = Client(
            name=session_name,
            api_id=int(session_data["api_id"]),
            api_hash=session_data["api_hash"]
        )

        try:
            await client.start()
            yield client
        finally:
            await client.stop()

    async def send_verification_code(self, session_name: str) -> Optional[str]:
        """Send verification code to the phone number."""
        async with self._get_client(session_name) as client:
            session_data = self._get_session_data(session_name)
            try:
                # Kurigram'da send_code method ishlatiladi
                result = await client.send_code(session_data["number"])
                return result.phone_code_hash
            except Exception as e:
                import logging
                logger = logging.getLogger(__name__)
                logger.error(f"Error sending verification code for {session_name}: {e}")
                return None

    async def verify_code(
        self,
        session_name: str,
        code: str,
        phone_code_hash: str,
        two_fa_password: str = None,
    ) -> bool:
        """
        Verify the code and sign in.
        """
        async with self._get_client(session_name) as client:
            session_data = self._get_session_data(session_name)
            try:
                # Kurigram'da sign_in method ishlatiladi
                result = await client.sign_in(
                    phone_number=session_data["number"],
                    phone_code_hash=phone_code_hash,
                    phone_code=code
                )
                
                # Agar User obyekti qaytarilsa, muvaffaqiyatli
                if hasattr(result, 'id'):
                    await self._store_user_id(session_name, result.id)
                    return True
                    
                return False
                
            except SessionPasswordNeeded:
                # 2FA parol kerak
                if two_fa_password:
                    try:
                        result = await client.check_password(two_fa_password)
                        if hasattr(result, 'id'):
                            await self._store_user_id(session_name, result.id)
                            return True
                    except Exception as e:
                        import logging
                        logger = logging.getLogger(__name__)
                        logger.error(f"2FA password error for {session_name}: {e}")
                        return False
                else:
                    raise ValueError("2FA password required but not provided")
            except Exception as e:
                import logging
                logger = logging.getLogger(__name__)
                logger.error(f"Error verifying code for {session_name}: {e}")
                return False

    async def _store_user_id(self, session_name: str, user_id: int) -> None:
        """
        Store user_id in the database session.
        """
        try:
            # Import here to avoid circular imports
            from userbot.models import Session

            session_obj = await sync_to_async(Session.objects.get)(session_name=session_name)
            if not session_obj.user_id:
                session_obj.user_id = user_id
                await sync_to_async(session_obj.save)()
        except Exception as e:
            # Log error but don't fail the verification process
            import logging
            logger = logging.getLogger(__name__)
            logger.error(f"Error storing user_id for session {session_name}: {e}")

    async def logout(self, session_name: str) -> bool:
        """
        Log out the user from the session.
        """
        async with self._get_client(session_name) as client:
            try:
                # Kurigram'da log_out method ishlatiladi
                await client.log_out()
                return True
            except Exception as e:
                import logging
                logger = logging.getLogger(__name__)
                logger.error(f"Error logging out session {session_name}: {e}")
                return False


session = KurigramSessionManager()
